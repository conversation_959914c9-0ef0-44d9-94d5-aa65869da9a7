"""
Wake Word + Real-time Streaming Auto-typing example for RealtimeSTT

This example demonstrates wake word activation combined with real-time streaming auto-typing functionality.
The application will:
1. Listen continuously for a wake word (like "jarvis")
2. When the wake word is detected, start recording your speech
3. Transcribe the speech to text using the local Whisper model with REAL-TIME STREAMING
4. Automatically type the transcribed text AS YOU SPEAK into whatever text field is currently active/focused

Features:
- Wake word activation (no constant recording)
- Real-time streaming transcription (text appears as you speak, like live captions)
- Smooth word-based streaming with minimal backspacing/corrections
- Append-only approach - new words are added without disrupting previous text
- Local processing only (no API tokens consumed)
- Auto-typing into any active text input field with live updates
- Conservative correction strategy (only last 2-3 words corrected if needed)
- Visual feedback and status updates

Usage:
1. Run this script
2. Open any text editor, word processor, or text input field
3. Click in the text field to give it focus
4. Say the wake word "jarvis" (or your chosen wake word)
5. Speak your message
6. Watch as your speech is automatically typed in real-time as you speak
7. The system will automatically correct and refine the text as the transcription stabilizes

Press Ctrl+C to exit.
"""

import os
import sys
import time
import threading

# Handle Windows-specific torch audio initialization
if os.name == "nt" and (3, 8) <= sys.version_info < (3, 99):
    try:
        from torchaudio._extension.utils import _init_dll_path
        _init_dll_path()
    except ImportError:
        pass

from RealtimeSTT import AudioToTextRecorder

# Audio feedback functionality
try:
    import pygame
    PYGAME_AVAILABLE = True
except ImportError:
    PYGAME_AVAILABLE = False
    pygame = None


class AudioFeedback:
    """Handles audio feedback for wake word detection and listening states"""

    def __init__(self, enable_audio=True, sounds_dir="sounds"):
        self.enable_audio = enable_audio and PYGAME_AVAILABLE
        self.sounds_dir = sounds_dir
        self.sounds = {}

        if self.enable_audio:
            try:
                # Initialize pygame mixer for audio playback
                pygame.mixer.pre_init(frequency=22050, size=-16, channels=2, buffer=512)
                pygame.mixer.init()
                self._load_sounds()
            except Exception as e:
                print(f"Warning: Could not initialize audio feedback: {e}")
                self.enable_audio = False

    def _load_sounds(self):
        """Load audio files for different states - using only listening_start.wav for all triggers"""
        # Use only listening_start.wav for all audio feedback
        listening_start_path = os.path.join(self.sounds_dir, 'listening_start.wav')

        if os.path.exists(listening_start_path):
            try:
                # Load the same sound for all triggers
                sound = pygame.mixer.Sound(listening_start_path)
                sound.set_volume(0.7)  # Set volume to a reasonable level (0.0 to 1.0)

                # Use the same sound for all events
                self.sounds['wake_detected'] = sound      # Sound when wake word is detected
                self.sounds['listening_start'] = sound    # Sound when listening starts
                self.sounds['listening_stop'] = sound     # Sound when listening stops

                print(f"Audio feedback loaded: using {listening_start_path} for all triggers")
            except Exception as e:
                print(f"Warning: Could not load sound {listening_start_path}: {e}")
        else:
            print(f"Warning: Sound file not found: {listening_start_path}")

    def play_sound(self, sound_name):
        """Play a specific sound in a separate thread to avoid blocking"""
        if self.enable_audio and sound_name in self.sounds:
            def play_async():
                try:
                    self.sounds[sound_name].play()
                except Exception as e:
                    print(f"Warning: Could not play sound {sound_name}: {e}")

            # Play sound in a separate thread to avoid blocking the main process
            threading.Thread(target=play_async, daemon=True).start()


class WakeWordAutoTyper:
    def __init__(self, enable_audio_feedback=True, sounds_dir="sounds"):
        self.wake_word_detected = False
        self.typing_active = False
        self.last_realtime_text = ""
        self.current_typed_length = 0

        # Add safeguards against duplicate processing
        self.last_processed_text = ""
        self.last_process_time = 0
        self.processing_lock = threading.Lock()

        # Streaming transcription state - word-based approach
        self.confirmed_words = []  # List of words that have been typed and confirmed
        self.pending_text = ""     # Current partial/unconfirmed text being typed
        self.last_full_text = ""   # Last complete text received
        self.streaming_lock = threading.Lock()

        # Word-level tracking for smoother updates
        self.last_word_count = 0
        self.partial_word_length = 0

        # Initialize audio feedback system
        self.audio_feedback = AudioFeedback(enable_audio_feedback, sounds_dir)
    
    def on_wakeword_detected(self):
        """Callback when wake word is detected"""
        self.wake_word_detected = True
        # Play wake word detection sound
        self.audio_feedback.play_sound('wake_detected')

    def on_wakeword_timeout(self):
        """Callback when wake word times out"""
        # Play listening stop sound when wake word times out
        self.audio_feedback.play_sound('listening_stop')

    def on_wakeword_detection_start(self):
        """Callback when wake word detection starts"""
        pass

    def on_wakeword_detection_end(self):
        """Callback when wake word detection ends"""
        pass

    def on_recording_start(self):
        """Callback when recording starts"""
        # Reset streaming state for new recording session
        with self.streaming_lock:
            self.confirmed_words = []
            self.pending_text = ""
            self.last_full_text = ""
            self.last_word_count = 0
            self.partial_word_length = 0

        # Play listening start sound when recording begins
        self.audio_feedback.play_sound('listening_start')

    def on_recording_stop(self):
        """Callback when recording stops"""
        # Play listening stop sound when recording ends
        self.audio_feedback.play_sound('listening_stop')

    def on_typing_start(self):
        """Callback when auto-typing starts"""
        self.typing_active = True

    def on_typing_complete(self):
        """Callback when auto-typing completes (not used since we disabled built-in auto-typing)"""
        self.typing_active = False

    def on_typing_error(self, error):
        """Callback when auto-typing encounters an error (not used since we disabled built-in auto-typing)"""
        self.typing_active = False

    def _extract_words_from_text(self, text):
        """Extract words from text, preserving spaces and punctuation context"""
        if not text:
            return []

        # Split by spaces but keep track of the original spacing
        words = []
        current_word = ""

        for char in text:
            if char.isspace():
                if current_word:
                    words.append(current_word)
                    current_word = ""
                # Add space as a separate "word" to preserve formatting
                if words:  # Don't add leading spaces
                    words.append(" ")
            else:
                current_word += char

        # Add the last word if there is one
        if current_word:
            words.append(current_word)

        return words

    def on_realtime_transcription_update(self, text):
        """Callback for real-time transcription updates - implements smooth word-based streaming"""
        if not text or not text.strip():
            return

        with self.streaming_lock:
            cleaned_text = text.strip()

            # Skip if text hasn't changed
            if cleaned_text == self.last_full_text:
                return

            try:
                import pyautogui

                # Extract words from the new text
                new_words = self._extract_words_from_text(cleaned_text)

                # Find how many words from the beginning match our confirmed words
                confirmed_count = len(self.confirmed_words)
                matching_words = 0

                for i in range(min(confirmed_count, len(new_words))):
                    if i < len(self.confirmed_words) and self.confirmed_words[i] == new_words[i]:
                        matching_words += 1
                    else:
                        break

                # If we have fewer matching words than confirmed, we need to backspace
                # But we'll be conservative and only backspace the last few words to minimize disruption
                if matching_words < confirmed_count:
                    # Only backspace if the difference is small (last 1-2 words)
                    # This prevents major disruptions while allowing minor corrections
                    words_to_remove = confirmed_count - matching_words
                    if words_to_remove <= 2:  # Only allow minor corrections
                        # Calculate characters to backspace
                        chars_to_remove = 0
                        for i in range(confirmed_count - words_to_remove, confirmed_count):
                            if i < len(self.confirmed_words):
                                chars_to_remove += len(self.confirmed_words[i])

                        # Backspace the characters
                        for _ in range(chars_to_remove):
                            pyautogui.press('backspace')

                        # Update confirmed words
                        self.confirmed_words = self.confirmed_words[:matching_words]

                # Clear any pending partial text
                if self.partial_word_length > 0:
                    for _ in range(self.partial_word_length):
                        pyautogui.press('backspace')
                    self.partial_word_length = 0
                    self.pending_text = ""

                # Type new confirmed words (complete words that are stable)
                confirmed_count = len(self.confirmed_words)
                if len(new_words) > confirmed_count:
                    # Determine which words to confirm (all but the last one, which might be partial)
                    words_to_confirm = new_words[confirmed_count:-1] if len(new_words) > 1 else []

                    # Type the confirmed words
                    for word in words_to_confirm:
                        pyautogui.typewrite(word, interval=0.005)
                        self.confirmed_words.append(word)

                    # Handle the last word (might be partial)
                    if new_words:
                        last_word = new_words[-1]
                        # Only type partial words if they're substantial (more than 2 characters)
                        # This reduces jitter from single character changes
                        if len(last_word) >= 3 or (len(new_words) == 1 and len(last_word) >= 1):
                            pyautogui.typewrite(last_word, interval=0.005)
                            self.pending_text = last_word
                            self.partial_word_length = len(last_word)

                self.last_full_text = cleaned_text
                self.last_realtime_text = cleaned_text

            except Exception as e:
                print(f"[ERROR] Streaming typing error: {e}")

    def on_realtime_transcription_stabilized(self, text):
        """Callback for stabilized real-time transcription - minimal final corrections only"""
        if not text or not text.strip():
            return

        with self.streaming_lock:
            cleaned_text = text.strip()

            # Only make corrections if the stabilized text is significantly different
            # and only correct the last few words to avoid major disruptions
            if cleaned_text != self.last_full_text:
                try:
                    import pyautogui

                    # Extract words from both texts
                    stabilized_words = self._extract_words_from_text(cleaned_text)
                    current_words = self.confirmed_words.copy()

                    # Add pending text as a word if it exists
                    if self.pending_text:
                        current_words.append(self.pending_text)

                    # Find the first difference
                    first_diff = len(stabilized_words)
                    for i in range(min(len(current_words), len(stabilized_words))):
                        if i >= len(current_words) or current_words[i] != stabilized_words[i]:
                            first_diff = i
                            break

                    # Only correct if the difference is in the last 3 words or less
                    # This prevents major text replacement while allowing final corrections
                    words_to_correct = len(current_words) - first_diff
                    if words_to_correct <= 3 and first_diff < len(stabilized_words):
                        # Calculate how many characters to backspace
                        chars_to_remove = 0
                        for i in range(first_diff, len(current_words)):
                            if i < len(current_words):
                                word = current_words[i]
                                chars_to_remove += len(word)

                        # Also remove pending partial text
                        chars_to_remove += self.partial_word_length

                        # Backspace the incorrect part
                        if chars_to_remove > 0:
                            for _ in range(chars_to_remove):
                                pyautogui.press('backspace')

                        # Type the corrected ending
                        corrected_part = stabilized_words[first_diff:]
                        for word in corrected_part:
                            pyautogui.typewrite(word, interval=0.01)

                        # Update our state
                        self.confirmed_words = stabilized_words.copy()
                        self.pending_text = ""
                        self.partial_word_length = 0
                        self.last_full_text = cleaned_text

                    # If no major correction needed, just confirm any pending text
                    elif self.pending_text and cleaned_text.endswith(self.pending_text):
                        # The pending text is confirmed, move it to confirmed words
                        if self.pending_text not in self.confirmed_words:
                            self.confirmed_words.append(self.pending_text)
                        self.pending_text = ""
                        self.partial_word_length = 0
                        self.last_full_text = cleaned_text

                except Exception as e:
                    print(f"[ERROR] Stabilized typing error: {e}")

    def process_text(self, text):
        """Process final transcribed text - only used as fallback when streaming fails"""
        import time
        import threading

        # Use lock to prevent concurrent execution
        with self.processing_lock:
            # Debug information
            current_time = time.strftime("%H:%M:%S")
            thread_id = threading.current_thread().ident

            if not text or not text.strip():
                return

            cleaned_text = text.strip()
            current_time_seconds = time.time()

            # Check for duplicate processing
            if (cleaned_text == self.last_processed_text and
                current_time_seconds - self.last_process_time < 2.0):  # 2 second window
                return

            # Only type if we haven't already streamed this text
            # (this serves as a fallback for when streaming fails)
            with self.streaming_lock:
                current_typed = " ".join(self.confirmed_words) + self.pending_text
                if cleaned_text != current_typed.strip():
                    try:
                        import pyautogui

                        # Clear any existing text first
                        total_chars = sum(len(word) for word in self.confirmed_words) + self.partial_word_length
                        if total_chars > 0:
                            for _ in range(total_chars):
                                pyautogui.press('backspace')

                        # Type the final text
                        pyautogui.typewrite(cleaned_text, interval=0.01)

                        # Update tracking variables to reflect the new state
                        self.confirmed_words = self._extract_words_from_text(cleaned_text)
                        self.pending_text = ""
                        self.partial_word_length = 0
                        self.last_full_text = cleaned_text

                    except Exception as e:
                        print(f"[ERROR] pyautogui error: {e}")

                # Update tracking variables
                self.last_processed_text = cleaned_text
                self.last_process_time = current_time_seconds

            # Reset counters for next session
            self.last_realtime_text = ""
            self.current_typed_length = 0
    
    def run(self):
        print("Ready - Say 'jarvis' then speak your message")
        print("Smooth real-time streaming transcription enabled - words appear as you speak")
        print("Minimal corrections approach - new words are added without disrupting previous text")
        print("Press Ctrl+C to exit")
        print()
        
        try:
            # Initialize the recorder with wake word + real-time auto-typing
            recorder = AudioToTextRecorder(
                # Model configuration
                model="small",  # Upgraded model for better transcription accuracy
                language="en",  # Set to your preferred language

                # Real-time transcription settings - ENABLED FOR STREAMING
                enable_realtime_transcription=True,  # Enable for streaming transcription
                realtime_model_type="tiny",  # Fast model for real-time processing
                realtime_processing_pause=0.02,  # Reduced from 0.05s to 0.02s for faster processing
                on_realtime_transcription_update=self.on_realtime_transcription_update,  # Enable streaming callback
                on_realtime_transcription_stabilized=self.on_realtime_transcription_stabilized,  # Enable stabilized callback

                # Wake word configuration - OPTIMIZED FOR LOW LATENCY
                wakeword_backend="pvporcupine",  # Use Porcupine backend
                wake_words="jarvis",  # Using "jarvis" as closest alternative to "boss"
                wake_words_sensitivity=0.6,  # Adjust sensitivity (0.0-1.0)
                wake_word_timeout=3,  # Reduced from 5s to 3s for faster timeout
                wake_word_activation_delay=0,  # Immediate activation

                # Wake word callbacks
                on_wakeword_detected=self.on_wakeword_detected,
                on_wakeword_timeout=self.on_wakeword_timeout,
                on_wakeword_detection_start=self.on_wakeword_detection_start,
                on_wakeword_detection_end=self.on_wakeword_detection_end,

                # Auto-typing configuration - COMPLETELY DISABLED
                enable_auto_typing=False,  # Disable built-in auto-typing completely
                auto_typing_delay=0.01,
                auto_typing_fail_safe=True,
                auto_typing_add_space=False,  # We handle spacing manually

                # Auto-typing callbacks - REMOVED to prevent interference
                on_auto_typing_start=None,  # Remove callback
                on_auto_typing_complete=None,  # Remove callback
                on_auto_typing_error=None,  # Remove callback

                # Recording callbacks
                on_recording_start=self.on_recording_start,
                on_recording_stop=self.on_recording_stop,

                # Voice activity detection settings - BALANCED FOR NATURAL SPEECH
                silero_sensitivity=0.4,  # Balanced sensitivity to avoid cutting off during thinking pauses
                silero_deactivity_detection=True,  # Use Silero for more robust end-of-speech detection
                webrtc_sensitivity=3,  # Standard sensitivity for reliable voice detection
                post_speech_silence_duration=0.9,  # Increased to 0.9s to allow for natural thinking pauses
                min_length_of_recording=0.4,  # Slightly increased to ensure complete words are captured

                # Audio quality settings - OPTIMIZED FOR LOW LATENCY
                beam_size=3,  # Reduced from 5 to 3 for faster transcription with minimal accuracy loss
                beam_size_realtime=1,  # Reduced from 3 to 1 for fastest real-time processing
                faster_whisper_vad_filter=True,  # Keep VAD filtering for cleaner audio
                normalize_audio=True,  # Keep audio normalization for consistency

                # UI settings
                spinner=False,  # Disable spinner for clean output

                # Logging settings
                no_log_file=True,  # Disable log file creation to eliminate unnecessary file I/O

                # Early transcription for reduced latency - BALANCED SETTING
                early_transcription_on_silence=4,  # Increased from 2 to 4 to avoid premature transcription during speech pauses
            )

            # CRITICAL: Explicitly disable the auto_typer to prevent any built-in auto-typing
            recorder.auto_typer = None
            recorder.enable_auto_typing = False

            # Additional safety check for any auto-typing mechanisms
            if hasattr(recorder, '_on_realtime_transcription_stabilized'):
                original_stabilized = recorder._on_realtime_transcription_stabilized
                def safe_stabilized_callback(text):
                    # Call original callback but without auto-typing
                    if recorder.on_realtime_transcription_stabilized:
                        recorder.on_realtime_transcription_stabilized(text)
                recorder._on_realtime_transcription_stabilized = safe_stabilized_callback

            # Main loop - continuously listen for wake word and process speech
            while True:
                try:
                    import time

                    # This will wait for wake word, then record and get transcription
                    # The streaming transcription will happen automatically via callbacks
                    start_time = time.time()
                    text = recorder.text()  # Get final text - streaming already handled by callbacks
                    end_time = time.time()

                    # The streaming callbacks handle real-time typing, but we can use this
                    # as a fallback to ensure the final text is correct
                    if text and text.strip():
                        # Only process if significantly different from streamed text
                        with self.streaming_lock:
                            current_typed = " ".join(self.confirmed_words) + self.pending_text
                            if text.strip() != current_typed.strip():
                                print(f"[DEBUG] Final text differs from streamed, correcting: '{text.strip()}'")
                                self.process_text(text)
                            else:
                                print(f"[DEBUG] Final text matches streamed text, no correction needed")
                    else:
                        print(f"[DEBUG] Skipping empty text from recorder")

                except KeyboardInterrupt:
                    print("\nStopping...")
                    break
                except Exception as e:
                    print(f"Error: {e}")
                    time.sleep(1)
                    
        except ImportError as e:
            if "pyautogui" in str(e):
                print("Error: pyautogui is required for auto-typing functionality.")
                print("Install it with: pip install pyautogui")
            elif "pvporcupine" in str(e):
                print("Error: pvporcupine is required for wake word detection.")
                print("Install it with: pip install pvporcupine")
            else:
                print(f"Import error: {e}")
            sys.exit(1)

        except Exception as e:
            print(f"Failed to initialize recorder: {e}")
            sys.exit(1)

        finally:
            try:
                recorder.shutdown()
            except:
                pass


def main():
    typer = WakeWordAutoTyper()
    typer.run()


if __name__ == "__main__":
    main()
